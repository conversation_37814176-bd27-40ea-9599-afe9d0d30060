# NocoBase 表格控件研究文档

## 概述

本目录包含了对 NocoBase 表格控件的深入研究文档，涵盖了 API 参考、源码分析和使用示例。

## 文档结构

### 📋 [API 参考](./API_REFERENCE.md)
- 表格级别操作 (Table Actions)
- 行级别操作 (Row Actions)
- 字段级别操作 (Field Operations)
- 权限控制体系
- 扩展机制

### 🔍 [源码分析](./SOURCE_CODE_ANALYSIS.md)
- 架构设计
- 核心组件分析
- 数据流分析
- 权限控制机制
- 扩展机制
- 性能优化

### 💡 [使用示例](./API_USAGE_EXAMPLES.md)
- 基础表格配置
- 表格操作配置
- 字段配置示例
- 高级配置示例
- 自定义操作示例
- 权限控制示例
- 完整示例
- 最佳实践

## 核心发现

### 1. 架构特点
- **模块化设计**: 每个功能都是独立的组件，便于维护和扩展
- **权限控制**: 完善的 ACL 机制，支持细粒度权限控制
- **数据驱动**: 基于 Schema 的配置方式，支持动态配置
- **性能优化**: 虚拟滚动、数据缓存等优化手段

### 2. 关键组件
- **TableBlock**: 表格块组件
- **TableV2**: 表格渲染引擎
- **SchemaInitializer**: 操作初始化器
- **ACLActionProvider**: 权限控制提供者

### 3. 扩展机制
- 自定义操作注册
- 自定义字段类型
- 自定义权限控制
- 自定义样式和主题

## 使用建议

1. **新手入门**: 先阅读 [API 参考](./API_REFERENCE.md) 了解基本概念
2. **深入理解**: 阅读 [源码分析](./SOURCE_CODE_ANALYSIS.md) 理解实现原理
3. **实践应用**: 参考 [使用示例](./API_USAGE_EXAMPLES.md) 进行开发

## 技术栈

- **前端框架**: React + TypeScript
- **UI 组件**: Ant Design
- **状态管理**: Formily
- **国际化**: i18next
- **构建工具**: Webpack

## 相关链接

- [NocoBase 官方文档](https://docs.nocobase.com/)
- [源码仓库](https://github.com/nocobase/nocobase)
- [社区论坛](https://github.com/nocobase/nocobase/discussions)

## 贡献指南

如果您发现文档中的错误或有改进建议，请提交 Issue 或 Pull Request。

---

*本文档基于 NocoBase 源码分析，版本信息请参考具体代码库。*